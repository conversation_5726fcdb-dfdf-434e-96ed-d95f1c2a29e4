body {
  margin: 0;
}

/* WORKAROUND FOR BWS HEADER */
body.space-theme-alfaromeo .cvHeader {
  background-color: #1C1F2A;
  position: static;
}
/* .space-theme-peugeot,
.space-theme-citroen,
.space-theme-fiat {
  background: #ffffff;
}
.space-theme-alfaromeo {
  background: #1c1f2a;
}
.space-theme-ds {
  background: #e9e7e1;
}
.space-theme-jeep {
  background: #f1f0f0;
}
.space-theme-opel {
  background: #f9f9f9;
}
.space-theme-abarth {
  background: #2c2c2c;
} */

@font-face {
  font-family: 'Encode Sans';
  src: url('/fonts/EncodeSans/EncodeSans-Medium.ttf') format('truetype'),
    url('/fonts/EncodeSans/EncodeSans-Bold.ttf') format('truetype'),
    url('/fonts/EncodeSans/EncodeSans-Light.ttf') format('truetype'),
    url('/fonts/EncodeSans/EncodeSans-Regular.ttf') format('truetype'),
    url('/fonts/EncodeSans/EncodeSans-Black.ttf') format('truetype'),
    url('/fonts/EncodeSans/EncodeSans-ExtraBold.ttf') format('truetype'),
    url('/fonts/EncodeSans/EncodeSans-ExtraLight.ttf') format('truetype'),
    url('/fonts/EncodeSans/EncodeSans-SemiBold.ttf') format('truetype'),
    url('/fonts/EncodeSans/EncodeSans-Thin.ttf') format('truetype'),
    url('/fonts/EncodeSans/EncodeSans_SemiExpanded-Bold.ttf') format('truetype');
}
@font-face {
  font-family: 'Stellantis UI';
  src: url('/fonts/StellantisUI/StellantisUI-Bold.ttf') format('truetype');
  font-weight: 700;
}
@font-face {
  font-family: 'Stellantis UI';
  src: url('/fonts/StellantisUI/StellantisUI-Light.ttf') format('truetype');
  font-weight: 200;
}
@font-face {
  font-family: 'Stellantis UI';
  src:url('/fonts/StellantisUI/StellantisUI-Regular.ttf') format('truetype');
  font-weight: 400;
}

@font-face {
  font-family: 'Sequel';
  src: url('/fonts/sequel-100-black/Sequel100Black-45.ttf') format('truetype');
  font-weight: normal;
}
@font-face {
  font-family: 'Citroen Type';
  src: url('/fonts/citroenType/CitroenType-Medium.ttf') format('truetype');
  font-weight: normal;
}
@font-face {
  font-family: 'DSAutomobiles';
  src: url('/fonts/DSAutomobiles/DSAutomobiles-Regular.otf') format('opentype');
  font-weight: normal;
}
@font-face {
  font-family: 'Proxima Nova';
  src: url('/fonts/proximaNova/Proxima-Nova-Regular.ttf') format('truetype');
  font-weight: normal;
}
@font-face {
  font-family: 'Proxima Nova';
  src: url('/fonts/proximaNova/Proxima-Nova-Bold.ttf') format('truetype');
  font-weight: bold;
}
@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 300;
  src: url('https://fonts.gstatic.com/s/roboto/v30/KFOlCnqEu92Fr1MmSU5fCRc4AMP6lbBP.woff2')
    format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F,
    U+FE2E-FE2F;
}

@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 300;
  src: url('https://fonts.gstatic.com/s/roboto/v30/KFOlCnqEu92Fr1MmSU5fABc4AMP6lbBP.woff2')
    format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}

@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 300;
  src: url('https://fonts.gstatic.com/s/roboto/v30/KFOlCnqEu92Fr1MmSU5fCBc4AMP6lbBP.woff2')
    format('woff2');
  unicode-range: U+1F00-1FFF;
}

@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 300;
  src: url('https://fonts.gstatic.com/s/roboto/v30/KFOlCnqEu92Fr1MmSU5fBxc4AMP6lbBP.woff2')
    format('woff2');
  unicode-range: U+0370-03FF;
}

@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 300;
  src: url('https://fonts.gstatic.com/s/roboto/v30/KFOlCnqEu92Fr1MmSU5fCxc4AMP6lbBP.woff2')
    format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1,
    U+01AF-01B0, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 300;
  src: url('https://fonts.gstatic.com/s/roboto/v30/KFOlCnqEu92Fr1MmSU5fChc4AMP6lbBP.woff2')
    format('woff2');
  unicode-range: U+0100-02AF, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF,
    U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 300;
  src: url('https://fonts.gstatic.com/s/roboto/v30/KFOlCnqEu92Fr1MmSU5fBBc4AMP6lQ.woff2')
    format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA,
    U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215,
    U+FEFF, U+FFFD;
}

@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 400;
  src: url('https://fonts.gstatic.com/s/roboto/v30/KFOmCnqEu92Fr1Mu72xKKTU1Kvnz.woff2')
    format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F,
    U+FE2E-FE2F;
}

@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 400;
  src: url('https://fonts.gstatic.com/s/roboto/v30/KFOmCnqEu92Fr1Mu5mxKKTU1Kvnz.woff2')
    format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}

@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 400;
  src: url('https://fonts.gstatic.com/s/roboto/v30/KFOmCnqEu92Fr1Mu7mxKKTU1Kvnz.woff2')
    format('woff2');
  unicode-range: U+1F00-1FFF;
}

@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 400;
  src: url('https://fonts.gstatic.com/s/roboto/v30/KFOmCnqEu92Fr1Mu4WxKKTU1Kvnz.woff2')
    format('woff2');
  unicode-range: U+0370-03FF;
}

@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 400;
  src: url('https://fonts.gstatic.com/s/roboto/v30/KFOmCnqEu92Fr1Mu7WxKKTU1Kvnz.woff2')
    format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1,
    U+01AF-01B0, U+1EA0-1EF9, U+20AB;
}
@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 400;
  src: url('https://fonts.gstatic.com/s/roboto/v30/KFOmCnqEu92Fr1Mu7GxKKTU1Kvnz.woff2')
    format('woff2');
  unicode-range: U+0100-02AF, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF,
    U+2113, U+2C60-2C7F, U+A720-A7FF;
}
@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 400;
  src: url('https://fonts.gstatic.com/s/roboto/v30/KFOmCnqEu92Fr1Mu4mxKKTU1Kg.woff2')
    format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA,
    U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215,
    U+FEFF, U+FFFD;
}
@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 700;
  src: url('https://fonts.gstatic.com/s/roboto/v30/KFOlCnqEu92Fr1MmWUlfCRc4AMP6lbBP.woff2')
    format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F,
    U+FE2E-FE2F;
}
@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 700;
  src: url('https://fonts.gstatic.com/s/roboto/v30/KFOlCnqEu92Fr1MmWUlfABc4AMP6lbBP.woff2')
    format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 700;
  src: url('https://fonts.gstatic.com/s/roboto/v30/KFOlCnqEu92Fr1MmWUlfCBc4AMP6lbBP.woff2')
    format('woff2');
  unicode-range: U+1F00-1FFF;
}
@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 700;
  src: url('https://fonts.gstatic.com/s/roboto/v30/KFOlCnqEu92Fr1MmWUlfBxc4AMP6lbBP.woff2')
    format('woff2');
  unicode-range: U+0370-03FF;
}
@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 700;
  src: url('https://fonts.gstatic.com/s/roboto/v30/KFOlCnqEu92Fr1MmWUlfCxc4AMP6lbBP.woff2')
    format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1,
    U+01AF-01B0, U+1EA0-1EF9, U+20AB;
}
@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 700;
  src: url('https://fonts.gstatic.com/s/roboto/v30/KFOlCnqEu92Fr1MmWUlfChc4AMP6lbBP.woff2')
    format('woff2');
  unicode-range: U+0100-02AF, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF,
    U+2113, U+2C60-2C7F, U+A720-A7FF;
}
@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 700;
  src: url('https://fonts.gstatic.com/s/roboto/v30/KFOlCnqEu92Fr1MmWUlfBBc4AMP6lQ.woff2')
    format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA,
    U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215,
    U+FEFF, U+FFFD;
}
@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 900;
  src: url('https://fonts.gstatic.com/s/roboto/v30/KFOlCnqEu92Fr1MmYUtfCRc4AMP6lbBP.woff2')
    format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F,
    U+FE2E-FE2F;
}
@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 900;
  src: url('https://fonts.gstatic.com/s/roboto/v30/KFOlCnqEu92Fr1MmYUtfABc4AMP6lbBP.woff2')
    format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 900;
  src: url('https://fonts.gstatic.com/s/roboto/v30/KFOlCnqEu92Fr1MmYUtfCBc4AMP6lbBP.woff2')
    format('woff2');
  unicode-range: U+1F00-1FFF;
}
@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 900;
  src: url('https://fonts.gstatic.com/s/roboto/v30/KFOlCnqEu92Fr1MmYUtfBxc4AMP6lbBP.woff2')
    format('woff2');
  unicode-range: U+0370-03FF;
}
@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 900;
  src: url('https://fonts.gstatic.com/s/roboto/v30/KFOlCnqEu92Fr1MmYUtfCxc4AMP6lbBP.woff2')
    format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1,
    U+01AF-01B0, U+1EA0-1EF9, U+20AB;
}
@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 900;
  src: url('https://fonts.gstatic.com/s/roboto/v30/KFOlCnqEu92Fr1MmYUtfChc4AMP6lbBP.woff2')
    format('woff2');
  unicode-range: U+0100-02AF, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF,
    U+2113, U+2C60-2C7F, U+A720-A7FF;
}
@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 900;
  src: url('https://fonts.gstatic.com/s/roboto/v30/KFOlCnqEu92Fr1MmYUtfBBc4AMP6lQ.woff2')
    format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA,
    U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215,
    U+FEFF, U+FFFD;
}

@font-face {
  font-family: Opel Next;
  src: url('/fonts/opelNext/OpelNext.otf') format('opentype');
  font-weight: normal;
}
@font-face {
  font-family: Opel Next;
  src: url('/fonts/opelNext/OpelNextBold.otf') format('opentype');
  font-weight: bold;
}
@font-face {
  font-family: Opel Next;
  src: url('/fonts/opelNext/OpelNextLight.otf') format('opentype');
  font-weight: 300;
}
@font-face {
  font-family: 'Poppins', sans-serif;
  src: url('/fonts/fiatPoppins/Poppins-Regular.ttf') format('truetype');
  font-weight: normal;
}
@font-face {
  font-family: 'Poppins', sans-serif;
  src: url('/fonts/fiatPoppins/Poppins-Bold.ttf') format('truetype');
  font-weight: bold;
}
@font-face {
  font-family: Peugeot, sans-serif;
  src: url('/fonts/peugeotNew/PeugeotNew-Regular.ttf') format('truetype');
  font-weight: normal;
}
@font-face {
  font-family: Peugeot, sans-serif;
  src: url('/fonts/peugeotNew/PeugeotNew-Bold.ttf') format('truetype');
  font-weight: bold;
}

@font-face {
  font-family: 'PSA Groupe HMI Sans';
  src: url('/fonts/psaGroupeHMISans/PSAGroupeHMISans-Regular.ttf') format('truetype');
  font-weight: normal;
}
@font-face {
  font-family: 'PSA Groupe HMI Sans';
  src: url('/fonts/psaGroupeHMISans/PSAGroupeHMISans-Bold.ttf') format('truetype');
  font-weight: bold;
}

@font-face {
  font-family: 'Abarth_Medium', sans-serif;
  src: url('/fonts/abarth/ABARTH-Medium.otf') format('opentype');
  font-weight: normal;
}
@font-face {
  font-family: 'Abarth_heavy', sans-serif;
  src: url('/fonts/abarth/ABARTH-Heavy.otf') format('opentype');
  font-weight: bold;
}
@font-face {
  font-family: 'Abarth_Light', sans-serif;
  src: url('/fonts/abarth/ABARTH-Light.otf') format('opentype');
  font-weight: 300;
}

/*
-----------------------
-----------------------
-----------------------
-----------------------
*/
/* GWP CWS ICON FONT */
/*
-----------------------
-----------------------
-----------------------
-----------------------
*/
@font-face {
  font-family: 'cws-icon';
  src: url('../gwp/cws-icon.ttf') format('truetype'),
    url('../gwp/cws-icon.woff') format('woff'),
    url('../gwp/cws-icon.svg') format('svg'),
    url('../gwp/cws-icon.eot') format('embedded-opentype'), ;
  font-weight: normal;
  font-style: normal;
  /* // font-display: block; */
}

/* // #region Roboto */
@font-face {
  font-family: 'Roboto';
  src: url('../gwp/Roboto-Regular.ttf') format('truetype');
  font-weight: normal;
}

@font-face {
  font-family: 'Roboto';
  src: url('../gwp/Roboto-Bold.ttf') format('truetype');
  font-weight: bold;
}

@font-face {
  font-family: 'Roboto';
  src: url('../gwp/Roboto-Italic.ttf') format('truetype');
  font-weight: normal;
  font-style: italic;
}

@font-face {
  font-family: 'Roboto';
  src: url('../gwp/Roboto-Medium.ttf') format('truetype');
  font-weight: 500;
}

@font-face {
  font-family: 'Roboto';
  src: url('../gwp/Roboto-MediumItalic.ttf') format('truetype');
  font-weight: 500;
  font-style: italic;
}

@font-face {
  font-family: 'Roboto';
  src: url('../gwp/Roboto-Black.ttf') format('truetype');
  font-weight: 900;
}

@font-face {
  font-family: 'Roboto';
  src: url('../gwp/Roboto-BlackItalic.ttf') format('truetype');
  font-weight: 900;
  font-style: italic;
}

/* // #endregion */

/* // #region ProximaNova */
@font-face {
  font-family: 'ProximaNova';
  src: url('../gwp/Proxima-Nova-Regular.ttf') format('truetype'),
    url('../gwp/Proxima-Nova-Regular.woff') format('woff'),
    url('../gwp/Proxima-Nova-Regular.svg') format('svg'),
    url('../gwp/Proxima-Nova-Regular.eot');
  font-weight: normal;
}

@font-face {
  font-family: 'ProximaNova';
  src: url('../gwp/Proxima-Nova-Bold.ttf') format('truetype'),
    url('../gwp/Proxima-Nova-Bold.woff') format('woff'),
    url('../gwp/Proxima-Nova-Bold.svg') format('svg'),
    url('../gwp/Proxima-Nova-Bold.eot');
  font-weight: bold;
}

/* // #endregion */

/* // #region FuturaPT */
@font-face {
  font-family: 'FuturaPT';
  src: url('../gwp/FuturaPT/FuturaPT-Demi.ttf') format('truetype'),
    url('../gwp/FuturaPT/FuturaPT-Demi.woff') format('woff'),
    url('../gwp/FuturaPT/FuturaPT-Demi.woff2') format('woff2'),
    url('../gwp/FuturaPT/FuturaPT-Demi.svg') format('svg'),
    url('../gwp/FuturaPT/FuturaPT-Demi.eot');
  font-weight: normal;
}

@font-face {
  font-family: 'FuturaPT';
  src: url('../gwp/FuturaPT/FuturaPT-Bold.ttf') format('truetype'),
    url('../gwp/FuturaPT/FuturaPT-Bold.woff') format('woff'),
    url('../gwp/FuturaPT/FuturaPT-Bold.woff2') format('woff2'),
    url('../gwp/FuturaPT/FuturaPT-Bold.svg') format('svg'),
    url('../gwp/FuturaPT/FuturaPT-Bold.eot');
  font-weight: bold;
}

@font-face {
  font-family: 'FuturaPT';
  src: url('../gwp/FuturaPT/FuturaPT-ExtraBold.ttf') format('truetype'),
    url('../gwp/FuturaPT/FuturaPT-ExtraBold.woff') format('woff'),
    url('../gwp/FuturaPT/FuturaPT-ExtraBold.woff2') format('woff2'),
    url('../gwp/FuturaPT/FuturaPT-ExtraBold.svg') format('svg'),
    url('../gwp/FuturaPT/FuturaPT-ExtraBold.eot');
  font-weight: 800;
}

/* // #endregion */

/* // #region NovecentoWide */
@font-face {
  font-family: 'NovecentoWide';
  src: url('../gwp/NovecentoWide.ttf') format('truetype'),
    url('../gwp/NovecentoWide.woff') format('woff'),
    url('../gwp/NovecentoWide.woff2') format('woff2'),
    url('../gwp/NovecentoWide.svg') format('svg'),
    url('../gwp/NovecentoWide.eot');
  font-weight: normal;
}

@font-face {
  font-family: 'NovecentoWideBold';
  src: url('../gwp/NovecentoWideBold.ttf') format('truetype'),
    url('../gwp/NovecentoWideBold.woff') format('woff'),
    url('../gwp/NovecentoWideBold.woff2') format('woff2'),
    url('../gwp/NovecentoWideBold.svg') format('svg'),
    url('../gwp/NovecentoWideBold.eot');
  font-weight: normal;
}

/* // #endregion */

/* // #region BreakersSlab */
@font-face {
  font-family: 'BreakersSlab';
  src: url('../gwp/BreakersSlab.ttf') format('truetype'),
    url('../gwp/BreakersSlab.woff') format('woff'),
    url('../gwp/BreakersSlab.woff2') format('woff2'),
    url('../gwp/BreakersSlab.svg') format('svg'),
    url('../gwp/BreakersSlab.eot');
  font-weight: normal;
}

@font-face {
  font-family: 'BreakersSlab';
  src: url('../gwp/BreakersSlabBold.ttf') format('truetype'),
    url('../gwp/BreakersSlabBold.woff') format('woff'),
    url('../gwp/BreakersSlabBold.woff2') format('woff2'),
    url('../gwp/BreakersSlabBold.svg') format('svg'),
    url('../gwp/BreakersSlabBold.eot');
  font-weight: normal;
}

/* // #endregion */

/* // #region ApexNew */
@font-face {
  font-family: 'ApexNew';
  src: url('../gwp/ApexNew-Regular.otf');
  font-weight: normal;
}

@font-face {
  font-family: 'ApexNew';
  src: url('../gwp/ApexNew-Bold.otf');
  font-weight: bold;
}

/* // #endregion */

/* // #region OpenSans */
@font-face {
  font-family: 'OpenSans';
  src: url('../gwp/OpenSans/OpenSans-Regular.ttf');
  font-weight: normal;
}

@font-face {
  font-family: 'OpenSans';
  src: url('../gwp/OpenSans/OpenSans-Italic.ttf');
  font-weight: normal;
  font-style: italic;
}

@font-face {
  font-family: 'OpenSans';
  src: url('../gwp/OpenSans/OpenSans-Bold.ttf');
  font-weight: bold;
}

@font-face {
  font-family: 'OpenSans';
  src: url('../gwp/OpenSans/OpenSans-BoldItalic.ttf');
  font-weight: bold;
  font-style: italic;
}

/* // #endregion */

/* // #region Universe */
@font-face {
  font-family: "Universe";
  font-weight: 300;
  src: url("../gwp/Universe/d708c032-d654-48b1-bb68-44f0bf9a1bc1.ttf");
}


@font-face {
  font-family: "Universe";
  font-weight: normal;
  src: url("../gwp/Universe/d708c032-d654-48b1-bb68-44f0bf9a1bc1.ttf");
}

@font-face {
  font-family: "Universe";
  font-weight: normal;
  font-style: italic;
  src: url("../gwp/Universe/2cbd7958-d41a-4a81-9101-40fa317b6694.ttf");
}

@font-face {
  font-family: "Universe";
  font-weight: 600;
  src: url("../gwp/Universe/7346456e-ae81-4596-a066-32e39b488d69.ttf");
}

@font-face {
  font-family: "Universe";
  font-weight: 600;
  font-style: oblique;
  src: url("../gwp/Universe/b6cc754a-7b02-4856-bb62-18b6c652515c.ttf");
}

@font-face {
  font-family: "Universe";
  font-weight: bold;
  src: url("../gwp/Universe/21ccefb3-0aad-40d7-9d90-998a75034a3c.ttf") format("truetype");
}

@font-face {
  font-family: "Universe";
  font-weight: bold;
  font-style: italic;
  src: url("../gwp/Universe/2e6c17ac-8379-4177-a5fb-6e42ecb58f6b.ttf") format("truetype");
}

/* // #endregion */

/* // #region PSA Default */
@font-face {
  font-family: 'PSA-Sans';
  src: url('../gwp/PSAGroupeHMISans-Regular.ttf') format('truetype'), ;
  font-weight: normal;
}

/* // #endregion */

/* // #region OpelNext */
@font-face {
  font-family: 'OpelNext';
  src: url('../gwp/Opel/OpelNext.ttf') format('truetype'),
    url('../gwp/Opel/OpelNext.woff') format('woff'),
    url('../gwp/Opel/OpelNext.woff2') format('woff2'),
    url('../gwp/Proxima-Nova-Regular.ttf');
  font-weight: normal;
}

/* // #endregion */

/* // #region Vauxhall */
@font-face {
  font-family: 'VauxhallNeue';
  src: url('../gwp/Vauxhall/VauxhallNeue-Regular.ttf') format('truetype'),
    url('../gwp/Vauxhall/VauxhallNeue-Regular.woff') format('woff'),
    url('../gwp/Proxima-Nova-Regular.ttf');
  font-weight: normal;
}

/* // #region Sequel100Black45 */
@font-face {
  font-family: 'Sequel100Black45';
  src: url('../gwp/Sequel100Black45.ttf');
  font-weight: bold;
}

/* // #region PSAGroupedHMISans */
@font-face {
  font-family: 'PSAGroupedHMISans';
  src: url('../gwp/PSAGroupeHMISans-Bold.ttf'),
    url('../gwp/PSAGroupeHMISans-Light.ttf'),
    url('../gwp/PSAGroupeHMISans-Regular.ttf'), ;
  font-weight: bold;
}

/* // #endregion */

/* // #region Peugeot */
@font-face {
  font-family: 'PeugeotNew-Regular';
  src: url('../gwp/Peugeot/PeugeotNew-Regular.ttf') format('truetype');
  font-weight: normal;
}

/* // #endregion */

/* // #region Citroen */
@font-face {
  font-family: 'CitroenType-Regular';
  src: url('../gwp/Citroen/CitroenType-Regular.ttf') format('truetype');
  font-weight: normal;
}

/* // #endregion */

/* // #region DS */
@font-face {
  font-family: 'DSAutomobiles-Regular';
  src: url('../gwp/DS/DSAutomobiles-Regular.ttf') format('truetype');
  font-weight: normal;
}

/* // #endregion */

/* // #region IBM Plex Sans */
@import url('https://fonts.googleapis.com/css2?family=IBM+Plex+Sans:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;1,100;1,200;1,300;1,400;1,500;1,600;1,700&display=swap');
/* // #endregion */

/* // #region EB Garamond */
@import url('https://fonts.googleapis.com/css2?family=EB+Garamond:ital,wght@0,400;0,500;0,600;0,700;0,800;1,400;1,500;1,600;1,700;1,800&display=swap');
/* // #endregion */

/* //#region ABARTH */
@font-face {
  font-family: "Abarth";
  font-weight: bold;
  src: url("../gwp/Abarth/ABARTH-Super.otf") format("opentype");
}

@font-face {
  font-family: "Abarth";
  font-weight: normal;
  src: url("../gwp/Abarth/ABARTH-Medium.otf") format("opentype");
}

/* //#endregion */
/* // #region Poppins */
@font-face {
  font-family: 'Poppins';
  src: url('../gwp/Poppins-Bold.ttf') format('truetype');
  font-weight: bold;
}

@font-face {
  font-family: 'Poppins';
  src: url('../gwp/Poppins-Regular.ttf') format('truetype');
  font-weight: normal;
}

/* // #endregion */

/* //#region ABARTH */
@font-face {
  font-family: "Abarth";
  font-weight: bold;
  src: url("../gwp/ABARTH-Super.otf") format("truetype");
}

@font-face {
  font-family: "Abarth";
  font-weight: normal;
  src: url("../gwp/ABARTH-Medium.otf") format("truetype");
}

/* //#endregion */

/* // #region Rajdhani (new RAM font after BreakersSlab) */
@font-face {
  font-family: 'Rajdhani';
  src: url('../gwp/Rajdhani/Rajdhani-Regular.ttf') format('truetype');
  font-weight: normal;
}

@font-face {
  font-family: 'Rajdhani';
  src: url('../gwp/Rajdhani/Rajdhani-Light.ttf') format('truetype'),
    url('../gwp/Rajdhani/Rajdhani-Medium.ttf') format('truetype');
  font-weight: normal;
}

@font-face {
  font-family: 'Rajdhani';
  src: url('../gwp/Rajdhani/Rajdhani-Bold.ttf') format('truetype'),
    url('../gwp/Rajdhani/Rajdhani-SemiBold.ttf') format('truetype');
  font-weight: bold;
}

/* // #endregion */

/* // #region Montserrat (Lancia main font) */
@font-face {
  font-family: 'Montserrat';
  src: url('../gwp/Montserrat/Montserrat-Regular.ttf') format('truetype');
  font-weight: normal;
}

@font-face {
  font-family: 'Montserrat';
  src: url('../gwp/Montserrat/Montserrat-Bold.ttf') format('truetype');
  font-weight: bold;
}

/* // #endregion */