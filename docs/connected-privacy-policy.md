# Connected Privacy Policy - Usage Guide

## Overview

The Connected Privacy Policy page has been restructured to provide a clean, organized way to handle different response types for privacy policy documents. It supports three main modes of operation:

1. **JSON Response** - Returns asset metadata and headers
2. **PDF Redirect** - Redirects directly to the CDN asset
3. **PDF Display** - Shows the PDF in an embedded viewer

## URL Structure

### Page Route
```
/connected-privacy-policy?country={COUNTRY}&language={LANGUAGE}&[additional_params]
```

### API Route
```
/api/connected-privacy-policy?country={COUNTRY}&language={LANGUAGE}&[additional_params]
```

## Parameters

### Required Parameters
- `country` - ISO country code (e.g., "US", "CA", "DE")
- `language` - Language code (e.g., "en", "fr", "de")

### Optional Parameters
- `view` - Response format (`json` to display JSON in UI)
- `type` - Response format (`json` for pure API JSON response without HTML)
- `pdf` - Set to `true` to redirect to CDN
- `page` - Page number to scroll to in PDF viewer
- `hideLoader` - Set to `true` to hide loading animations

## Usage Examples

### 1. Display PDF in Viewer (Default)
```
/connected-privacy-policy?country=US&language=en
```
This will display the PDF in an embedded viewer with navigation controls.

### 2. Get JSON Response with Asset Information
```
/connected-privacy-policy?country=US&language=en&view=json
```
or
```
/connected-privacy-policy?country=US&language=en&type=json
```

Returns JSON with asset metadata:
```json
{
  "url": "https://example.com/path/to/pdf",
  "status": 200,
  "statusText": "OK",
  "headers": {
    "contentType": "application/pdf",
    "contentLength": "1234567",
    "lastModified": "Wed, 21 Oct 2023 07:28:00 GMT",
    "etag": "\"abc123\"",
    "cacheControl": "public, max-age=3600"
  },
  "redirectUrl": "https://example.com/path/to/pdf",
  "pdfUrl": "https://example.com/path/to/pdf",
  "timestamp": "2023-10-21T07:28:00.000Z"
}
```

### 3. Redirect to CDN
```
/connected-privacy-policy?country=US&language=en&pdf=true
```
This will redirect the user directly to the PDF file on the CDN.

### 4. API Endpoint Usage
```bash
# Get JSON response
curl "/api/connected-privacy-policy?country=US&language=en&view=json"

# Get redirect (will return 302 status)
curl -I "/api/connected-privacy-policy?country=US&language=en&pdf=true"
```

## Features

### Error Handling
- Invalid country/language combinations show user-friendly error messages
- Network errors are handled gracefully
- Fallback to homepage for unrecoverable errors

### JSON Response Features
- Complete asset metadata including headers
- Timestamp of when the information was retrieved
- Copy-to-clipboard functionality
- Direct links to open the PDF

### PDF Viewer Features
- Responsive design that works on mobile and desktop
- Page navigation and zoom controls
- Direct linking to specific pages using the `page` parameter
- Loading states with customizable loader visibility

## File Structure

```
src/
├── pages/
│   ├── connected-privacy-policy/
│   │   └── index.jsx                 # Main page component
│   └── api/
│       └── connected-privacy-policy.js # API endpoint
├── utils/
│   └── connectedPrivacyPolicy.js     # Shared utility functions
└── components/
    └── PdfViewer.jsx                 # PDF display component
```

## Utility Functions

The `src/utils/connectedPrivacyPolicy.js` file contains shared functions:

- `fetchRegionConfig()` - Fetches configuration data
- `validateCountryAndLanguage()` - Validates input parameters
- `getAssetUrl()` - Generates PDF asset URLs
- `fetchAssetHeaders()` - Retrieves asset metadata
- `processPrivacyPolicyRequest()` - Main processing function

## Configuration

The system relies on a configuration file at:
```
/connected-vehicle-privacy-policy/assets/config.json
```

This file should contain:
- Regional mappings
- Country-to-region associations
- Supported languages per country
- EEA country listings

## Error Responses

### 400 Bad Request
```json
{
  "error": "Invalid parameters",
  "message": "Language 'xx' not supported for country 'US'. Available languages: en, es"
}
```

### 500 Internal Server Error
```json
{
  "error": "Internal server error",
  "message": "Failed to process request"
}
```

## Browser Support

- Modern browsers with ES6+ support
- PDF.js for PDF rendering
- Responsive design for mobile devices
- Graceful degradation for older browsers
