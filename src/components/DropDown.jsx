import React, { useState, useRef, useEffect, use } from "react";
import { ChevronDown, ChevronUp } from "lucide-react";

export default function DropDown({ options, onSelect, placeholder, title }) {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedOption, setSelectedOption] = useState({ value: placeholder });
  const dropdownRef = useRef(null);
  const [multiple, setMultiple] = useState(
    !(options.length == 2 && options[0].key == "name")
  );

  useEffect(() => {
    function handleClickOutside(event) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  useEffect(() => {
    setMultiple(
      !(
        (options.length == 2 && options[0].key == "name") ||
        options.length == 1
      )
    );
  }, [options]);

  useEffect(() => {
    setSelectedOption({ value: placeholder });
  }, [placeholder]);

  const toggleDropdown = () => setIsOpen(!isOpen);

  const handleSelect = (key, value) => {
    setSelectedOption({ key, value });
    setIsOpen(false);
    if (onSelect) {
      onSelect({ key, value });
    }
  };

  const displayValue = selectedOption ? selectedOption.value : placeholder;

  return (
    <div className="relative w-auto" ref={dropdownRef}>
      {title && (
        <label className="block font-extrabold text-sm text-gray-700 py-2">
          {title}
        </label>
      )}
      <button
        onClick={toggleDropdown}
        disabled={!multiple}
        className="flex items-center justify-between w-full h-12 px-4 text-left bg-gray-100 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-100"
      >
        <span className="text-gray-900 py-16">{displayValue}</span>
        {multiple &&
          (isOpen ? (
            <ChevronUp className="w-5 h-5 text-gray-700" />
          ) : (
            <ChevronDown className="w-5 h-5 text-gray-700" />
          ))}
      </button>
      {isOpen && (
        <div className="absolute w-full mt-2 bg-white rounded-md shadow-lg z-10 max-h-60 overflow-y-auto">
          <div className="p-2">
            {Object.entries(options).map((option) =>
              option[1].key !== "name" ? (
                <button
                  key={option[1].key}
                  onClick={() => handleSelect(option[1].key, option[1].value)}
                  className="w-full px-4 py-2 h-12 text-left text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 focus:outline-none focus:bg-gray-200 mb-1 last:mb-0"
                >
                  {option[1].value}
                </button>
              ) : null
            )}
          </div>
        </div>
      )}
    </div>
  );
}
