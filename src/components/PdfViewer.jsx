import { useCallback, useEffect, useRef, useState } from 'react';
import { useResizeObserver } from '@wojtekmaj/react-hooks';
import { pdfjs, Document, Page } from 'react-pdf';
import { ThreeDots } from 'react-loader-spinner';
import 'react-pdf/dist/esm/Page/AnnotationLayer.css';
import 'react-pdf/dist/esm/Page/TextLayer.css';

pdfjs.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`;

const options = {
  cMapUrl: '/cmaps/',
  standardFontDataUrl: '/standard_fonts/',
};

const maxWidth = 800;

export default function PdfViewer({ pdfUrl, hideLoader = false, pageno = 0 }) {
  const [numPages, setNumPages] = useState();
  const [containerRef, setContainerRef] = useState(null);
  const [containerWidth, setContainerWidth] = useState();
  const pageRefs = useRef([]);

  const onResize = useCallback((entries) => {
    const [entry] = entries;
    if (entry) {
      setContainerWidth(entry.contentRect.width);
    }
  }, []);

  useResizeObserver(containerRef, {}, onResize);

  const onDocumentLoadSuccess = ({ numPages: nextNumPages }) => {
    setNumPages(nextNumPages);
  };

  // Scroll to specified page when document and pages are loaded
  useEffect(() => {
    if (!pageno || !numPages) return;
  
    const scrollToPage = () => {
      const targetRef = pageRefs.current[pageno - 1];
      if (targetRef && typeof targetRef.scrollIntoView === 'function') {
        targetRef.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }
    };
  
    // Delay to ensure pages are rendered
    const timeoutId = setTimeout(scrollToPage, 300); // Adjust if needed
  
    return () => clearTimeout(timeoutId); // Cleanup
  }, [numPages, pageno]);

  return (
    <div className="flex flex-col items-center px-4 py-8">
      <div className="w-full" ref={setContainerRef}>
        <Document
          file={pdfUrl}
          onLoadSuccess={onDocumentLoadSuccess}
          options={options}
          loading={
            !hideLoader && (
              <div className="w-full h-screen flex items-center justify-center text-3xl bg-white text-black">
                <ThreeDots
                  visible={true}
                  height="80"
                  width="80"
                  color="#4766FF"
                  radius="9"
                  ariaLabel="three-dots-loading"
                />
              </div>
            )
          }
          error={<p>Failed to load PDF.</p>}
          noData={<p>No PDF file specified.</p>}
        >
          {numPages &&
            Array.from(new Array(numPages), (_, index) => (
              <div
                key={`page_wrapper_${index}`}
                className="flex flex-col items-center mb-6 relative"
                ref={(el) => (pageRefs.current[index] = el)} 
              >
                <div className="bg-white w-full max-w-[800px] relative shadow-md">
                  <div className="absolute top-1 right-1 text-xs text-gray-800">
                    {index + 1}
                  </div>
                  <div className="absolute bottom-1 right-1 text-xs text-gray-800">
                    {index + 1}
                  </div>
                  <Page
                    pageNumber={index + 1}
                    width={containerWidth ? Math.min(containerWidth, maxWidth) : maxWidth}
                    loading={hideLoader ? null : undefined}
                  />
                </div>
              </div>
            ))}
        </Document>
      </div>
    </div>
  );
}
 