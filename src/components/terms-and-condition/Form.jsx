'use client';
import React, { useEffect, useState } from "react";
import Header from "../header";
import DropDown from "../DropDown";
import Footer from "../Footer";
import { ThreeDots } from "react-loader-spinner";

function Form({ hideLoader }) {
  const [regionOptions, setRegionOptions] = useState({});
  const [pdf, setPdf] = useState(null);
  const [region, setRegion] = useState(null);
  const [country, setCountry] = useState(null);
  const [language, setLanguage] = useState(null);
  const [languageNames, setLanguageNames] = useState({});
  const [currentLanguage, setCurrentLanguage] = useState("en");
  const [translations, setTranslations] = useState({});
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || '';

  const translate = (key) =>
    translations && translations[currentLanguage]
      ? translations[currentLanguage][key]
      : key;
  const [text, setText] = useState(null);

  useEffect(() => {
    const fetchRegions = async () => {
      try {
        const response = await fetch(`${baseUrl}/terms-and-condition-app/assets/config.json`);
        const data = await response.json();
        console.log(data);
        setRegionOptions(data.brands);
        setLanguageNames(data.languages);
      } catch (err) {
        console.log(err.message);
      }
    };
    const fetchTranslations = async () => {
      try {
        const response = await fetch(`${baseUrl}/terms-and-condition-app/assets/translations.json`);
        const data = await response.json();
        setTranslations(data);
        return data;
      } catch (err) {
        console.log(err.message);
      }
    };
    fetchRegions();
    fetchTranslations();
  }, []);

  useEffect(() => {
    if (translations) {
      // Only access navigator on client side to prevent hydration issues
      if (typeof window !== 'undefined') {
        const browserLanguage = navigator.language.split("-")[0];
        if (translations[browserLanguage]) {
          setCurrentLanguage(browserLanguage);
        }
      }
      setText(translate("select_option_1"));
    }
  }, [translations]);

  useEffect(() => {
    if (region && country && language) {
      setPdf(`${region}_${country}_${language}.pdf`);
    }
  }, [region, country, language]);

  useEffect(() => {
    setCountry(null);
    setLanguage(null);
    if (region) {
      const regionData = regionOptions[region];
      setText(translate("select_option_2"));
      if (Object.keys(regionData).length === 2) {
        for (const [key, value] of Object.entries(regionData)) {
          if (value.name) {
            handleCountrySelect({ key });
          }
        }
      }
    }
  }, [region]);

  useEffect(() => {
    setLanguage(null);
    if (region && country) {
      const countryData = regionOptions[region][country];
      setText(translate("select_option_3"));
      if (Object.keys(countryData).length === 2) {
        const language = countryData.languages;
        if (language.includes(currentLanguage)) {
          handleLanguageSelect({ key: currentLanguage });
        } else {
          handleLanguageSelect({ key: language[0] });
        }
      }
    }
  }, [region, country]);

  const handleRegionSelect = (option) => {
    if(region === option.key) return
    setRegion(option.key);
    setCountry(null);
    setLanguage(null);
    setPdf("");
  };

  const handleCountrySelect = (option) => {
    if(country === option.key) return
    setCountry(option.key);
    setLanguage(null);
    setPdf("");
  };

  const handleLanguageSelect = (option) => {
    if(language === option.key) return
    setLanguage(option.key);
    const filename = `${region}_${country}_${option.key}.pdf`;
    setPdf(filename);
  };

  if (Object.keys(translations).length === 0) {
    return (
      <div className="w-full h-screen flex items-center justify-center text-3xl bg-white text-black">
        {!hideLoader && (
        <ThreeDots
          visible={true}
          height="80"
          width="80"
          color="#4766FF"
          radius="9"
          ariaLabel="three-dots-loading"
          wrapperStyle={{}}
          wrapperClass=""
        />)}
      </div>
    );
  }

  return (
    <>
      <div className="md:absolute md:right-0 md:top-0 md:bottom-0 md:w-1/3 bg-white  flex flex-col justify-between min-h-screen md:min-h-0">
        <div className="space-y-6 p-6">
          <Header translate={translate} />
          <div className="mt-7 space-y-4">
            <DropDown
              options={Object.entries(regionOptions).map(([key, { name }]) => ({
                key,
                value: name,
              }))}
              onSelect={handleRegionSelect}
              placeholder={
                region
                  ? `${regionOptions[region]["name"]}`
                  : translate("select_option_1")
              }
              title={translate("option_1")}
            />
            {region && (
              <DropDown
                options={Object.entries(regionOptions[region]).map(
                  ([key, { name }]) => ({ key, value: name })
                )}
                onSelect={handleCountrySelect}
                placeholder={
                  country
                    ? `${regionOptions[region][country]["name"]}`
                    : translate("select_option_2")
                }
                title={translate("option_2")}
              />
            )}
            {region && country && (
              <DropDown
                key={language - `${language}`}
                options={Object.entries(
                  regionOptions[region][country]["languages"]
                ).map((key) => ({ key: key[1], value: languageNames[key[1]] }))}
                onSelect={handleLanguageSelect}
                placeholder={
                  language
                    ? `${languageNames[language]}`
                    : translate("select_option_3")
                }
                title={translate("option_3")}
              />
            )}
          </div>
        </div>
        <Footer
          disabled={!(region && country && language)}
          translate={translate}
          baseUrl={baseUrl}
          pdf={pdf}
          country={country}
          language={language}
          text={text}
          brand={region}
        />
      </div>
    </>
  );
}
export default Form;
