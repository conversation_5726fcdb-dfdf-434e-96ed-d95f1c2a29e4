import React from 'react';

const Footer = ({ disabled = true, handleOnClick, translate }) => {
  return (
    <div className="sticky bottom-0 w-full bg-white shadow-[0_-4px_6px_-1px_rgba(0,0,0,0.1)] p-4">
        <button
          type="button"
          className={`w-full font-bold py-3 px-4 rounded transition duration-300 ease-in-out ${
            !(disabled) 
              ? 'bg-gray-400 cursor-not-allowed' 
              : 'bg-blue-600 hover:bg-blue-700 text-white'
          }`}
          disabled={!disabled}
          onClick={handleOnClick}
        >
          {translate('rtn_read_document')}
        </button>
      </div>
  );
};

export default Footer;