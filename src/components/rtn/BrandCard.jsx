import React, { useState } from "react";
import Image from "next/image";

const BrandCard = ({
  logoSrc,
  altText,
  setBrand,
  isSelected = false,
  width = 40,
  height = 40,
}) => {
  const handleClick = () => {
    setBrand(altText);
  };

  return (
    <div
      className={`rounded-lg shadow-lg p-4 m-3 flex items-center justify-center aspect-square transition-colors duration-300 ${
        isSelected ? "bg-gray-200" : "bg-white"
      }`}
    >
      <div className="relative w-16 h-16" onClick={handleClick}>
        <Image
          src={logoSrc}
          alt={altText}
          fill
          style={{ objectFit: "contain" }}
        />
      </div>
    </div>
  );
};

export default BrandCard;
