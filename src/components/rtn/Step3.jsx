import React from 'react'
import XF from './XF'
import XP from './XP'

function Step3({ translate, choosenBrand, brandConfig }) {
  if(!choosenBrand)
    return (
      <>
        Loading
      </>
    )
  return (
    <>
    {choosenBrand["leagacy"]==="xp" && (<XP translate={translate} brand={choosenBrand["name"]} brandConfig={brandConfig} />)}
    {choosenBrand["leagacy"]==="xf" && (<XF translate={translate} brand={choosenBrand["name"]} brandConfig={brandConfig} />)}

    </>

  )
}

export default Step3