import React from 'react'

function XF({translate, brand, brandConfig}) {
  if (!brandConfig) {
    return(
      <>
        <h1 className="text-2xl font-bold text-red-700 flex items-center justify-center">{translate("rtn_error_msg")}</h1>
      </>
    )
  }
  const redirectURL = brandConfig.redirectURL;
  const phoneNo = brandConfig.translatable == "1" ? brandConfig.phoneNoTR : brandConfig.phoneNo;

  return (
    
    <>
    <div className="max-w-2xl mx-auto text-black">

      <div className="space-y-8">
        <p className="text-xl">{translate('rtn_step3_title')}:</p>

        <div className="space-y-8">
          <div className="flex gap-4">
            <span className="text-3xl font-bold">1</span>
            <div className="space-y-4">
              <p className="text-lg">{translate('rtn_step3_description_xp1')}:</p>
              <ul className="list-disc pl-6 space-y-2">
                <li>{translate('rtn_step3_first').replace('$Brand', brand)}</li>
                <li>{translate('rtn_step3_second')}</li>
                <li>{translate('rtn_step3_third')}</li>
                <li>{translate('rtn_step3_fourth')}</li>

              </ul>
              <a href={`${redirectURL}`} target="_blank" className="text-blue-600 p-0 h-auto font-normal">
              {translate('rtn_step3_website_link1').replace('$Brand', brand)}
              </a>
            </div>
          </div>

          <div className="flex gap-4">
            <span className="text-3xl font-bold">2</span>
            <div className="space-y-4">
              <p className="text-lg">
              {translate('rtn_step3_description_xp2').replace('$Brand', brand)}
                
              </p>
              <a href={`tel:${phoneNo}`} className="text-blue-600 p-0 h-auto font-normal">
              {translate('rtn_step3_website_link2').replace('$Brand', brand)}
                
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
    </>
  )
}

export default XF