'use client';
import React, { useEffect, useState } from "react";
import Header from "./header";
import DropDown from "../DropDown";
import Footer from "./Footer";
import Step2 from "./Step2";
import Step3 from "./Step3";

function Form() {
  const [regionOptions, setRegionOptions] = useState({});
  const [supportedLanguages, setSupportedLanguages] = useState([]);
  const [brandMap, setBrandMap] = useState({});
  const [info, setInfo] = useState();
  const [footerBTN, setFooterBTN] = useState(false);
  const [btn, setBtn] = useState(true);
  const [brand, setBrand] = useState(null);
  const [region, setRegion] = useState(null);
  const [country, setCountry] = useState(null);
  const [language, setLanguage] = useState(null);
  const [brandConfig, setBrandConfig] = useState(null);
  const [error, setError] = useState(false);

  const [languageNames, setLanguageNames] = useState({});
  const [step, setStep] = useState(1);
  const [currentLanguage, setCurrentLanguage] = useState("en");
  const [translations, setTranslations] = useState(null);
  const [loading, setLoading] = useState(true);

  const translate = (key) => translations[key] || key;
  useEffect(() => {
    const fetchConfig = async () => {
      try {
        const res = await fetch(
          `https://media.space.awsmpsa.com/uploads/WEB/XX/XX/RTN/config.json?_=${Date.now()}`,
          { cache: "no-store" }
        );
        if (!res.ok) {
          setError(true);
          throw new Error(
            `Failed to fetch translations for ${browserLanguage}`
          );
        }
        const config = await res.json();
        setRegionOptions(config.regions);
        setLanguageNames(config.languages);
        setSupportedLanguages(config.supportedLanguages);
        setBrandMap(config.brandMap);
      } catch (e) {
        console.error("Error loading translations:", e);
      }
    };
    fetchConfig();
  }, []);

  useEffect(() => {
    if (translations) {
      setLoading(false);
    }
  }, [translations]);

  useEffect(() => {
    const fetchTranslations = async () => {
      let browserLanguage = navigator.language.split("-")[0];
      if (!supportedLanguages.includes(browserLanguage)) {
        browserLanguage = "en";
      }

      if (supportedLanguages.includes(browserLanguage)) {
        setCurrentLanguage(browserLanguage);
        try {
          const res = await fetch(
            `https://settings.spacemdw.com/?brand=ac&source=web&culture=${browserLanguage}-AQ&featurename=localization&_=${Date.now()}`,
            { cache: "no-store" }
          );
          if (!res.ok) {
            setError(true);
            throw new Error(
              `Failed to fetch translations for ${browserLanguage}`
            );
          }
          const data = await res.json();
          setTranslations(data);
        } catch (e) {
          console.error("Error loading translations:", e);
        }
      }
    };
    fetchTranslations();
  }, [supportedLanguages]);

  useEffect(() => {
    if (region && country && language) {
      setInfo(`${region}_${country}_${language}_${brand}`);
    }
  }, [region, country, language]);

  useEffect(() => {
    setCountry(null);
    setLanguage(null);
    if (region) {
      const regionData = regionOptions[region];

      if (Object.keys(regionData).length === 2) {
        for (const [key, value] of Object.entries(regionData)) {
          if (value.name) {
            handleCountrySelect({ key });
          }
        }
      }
    }
  }, [region]);

  useEffect(() => {
    setLanguage(null);
    if (region && country) {
      const countryData = regionOptions[region][country];

      if (Object.keys(countryData).length === 2) {
        const language = countryData.languages;
        if (language.includes(currentLanguage)) {
          handleLanguageSelect({ key: currentLanguage });
        } else {
          handleLanguageSelect({ key: language[0] });
        }
      }
    }
  }, [region, country]);

  const handleRegionSelect = (option) => {
    setRegion(option.key);
    setCountry(null);
    setLanguage(null);
    setInfo("");
  };

  const handleCountrySelect = (option) => {
    setCountry(option.key);
    setLanguage(null);
    setInfo("");
  };

  const handleLanguageSelect = (option) => {
    setLanguage(option.key);
    const filename = `${region}_${country}_${option.key}`;
    setInfo(filename);
    setFooterBTN(true);
    console.log(filename);
  };

  const handleBrandSelect = (option) => {
    setBrand(option);
    setFooterBTN(true);
  };

  const handleContinue = () => {
    console.log(info);
    if (step === 1) {
      setStep(2);
      setFooterBTN(false);
    }
    if (step === 2) {
      setStep(3);
      setBtn(false);
      setFooterBTN(false);
    }
  };

  useEffect(() => {
    const fetchConfig = async () => {
      try {
        setLoading(true);
        const res = await fetch(
          `https://settings.spacemdw.com/?brand=${brand}&culture=${language}-${country}&source=web&widgetname=gp-stellantis-rtn&_=${Date.now()}`,
          { cache: "no-store" }
        );        
        if (!res.ok) {
          throw new Error(
            `Failed to fetch translations for ${browserLanguage}`
          );
        }
        const config = await res.json();
        setBrandConfig(config["features"][0]["attributes"]);
        setLoading(false);
      } catch (e) {
        console.error("Error loading translations:", e);
        setLoading(false);
      }
    };
    if (brand && region && country && language) {
      fetchConfig();
    }
  }, [step]);

  if (error) {
    return (
      <>
        <h1 className="text-2xl font-bold text-red-700 flex items-center justify-center">
          {translate("rtn_error_msg")}
        </h1>
      </>
    );
  }

  if (loading) {
    return (
      <div className="flex flex-col h-screen bg-white">
        <div className="flex-grow overflow-y-auto">
          <div className="p-4 md:p-6 space-y-6">
            <div className="flex justify-center items-center h-full">
              Loading....
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-screen bg-white">
      <div className="flex-grow overflow-y-auto">
        <div className="p-4 md:p-6 space-y-6">
          <Header translate={translate} />
          {step == 1 && (
            <div className="space-y-4">
              <DropDown
                options={Object.entries(regionOptions).map(
                  ([key, { name }]) => ({
                    key,
                    value: name,
                  })
                )}
                onSelect={handleRegionSelect}
                placeholder={
                  region
                    ? `${regionOptions[region]["name"]}`
                    : translate("rtn_select_region")
                }
                title={
                  region
                    ? translate("rtn_region")
                    : translate("rtn_select_region")
                }
              />
              {region && (
                <DropDown
                  options={Object.entries(regionOptions[region])
                    .filter(([key]) => key !== "name")
                    .map(([key, { name }]) => ({ key, value: name }))}
                  onSelect={handleCountrySelect}
                  placeholder={
                    country
                      ? `${regionOptions[region][country]["name"]}`
                      : translate("rtn_select_country")
                  }
                  title={
                    country
                      ? translate("rtn_country")
                      : translate("rtn_select_country")
                  }
                />
              )}
              {region && country && (
                <DropDown
                  key={`language-${language}`}
                  options={regionOptions[region][country]["languages"].map(
                    (lang) => ({
                      key: lang,
                      value: languageNames[lang],
                    })
                  )}
                  onSelect={handleLanguageSelect}
                  placeholder={
                    language
                      ? `${languageNames[language]}`
                      : translate("rtn_select_language")
                  }
                  title={
                    language
                      ? translate("rtn_language")
                      : translate("rtn_select_language")
                  }
                />
              )}
            </div>
          )}
          {step == 2 && (
            <Step2
              translate={translate}
              setBrand={handleBrandSelect}
              choosenBrand={brand}
            />
          )}
          {step == 3 && (
            <Step3
              translate={translate}
              choosenBrand={brandMap[brand]}
              brandConfig={brandConfig}
            />
          )}
        </div>
      </div>
      {btn && (
        <Footer
          disabled={footerBTN}
          handleOnClick={handleContinue}
          translate={translate}
        />
      )}
    </div>
  );
}
export default Form;
