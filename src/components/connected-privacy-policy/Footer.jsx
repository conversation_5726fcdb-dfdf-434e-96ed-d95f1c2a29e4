import React, { useState } from "react";
import { AlertTriangle } from "lucide-react";
import { useRouter } from 'next/router';

const Footer = ({ disabled = true, translate, pdf,country, language, text, baseUrl }) => {
  const [alertVisible, setAlertVisible] = useState(false);
  const router = useRouter();

  const handleReadDocument = () => {
    if (pdf) {
      if (country || language) {
        router.push(`/connected-privacy-policy?country=${country}&language=${language}`);
        return;
      }
    } else {
      setAlertVisible(true);
      setTimeout(() => {
        setAlertVisible(false);
      }, 5000);
    }
  };
  return (
    <>
      <div className="sticky bottom-0 ">
        {alertVisible ? (
          <div className="flex items-center p-4 bg-black text-white rounded-md shadow-lg mb-4 mx-3">
            <AlertTriangle className="w-6 h-6 mr-3" color="red" />
            <span>{text}</span>
          </div>
        ) : (
          <div className="bg-white w-full py-4 px-5 font-light text-sm text-black">
            {translate("disclaimer")}
          </div>
        )}

        <div className="w-full bg-white shadow-[0_-4px_6px_-1px_rgba(0,0,0,0.1)] p-4">
          <button
            type="button"
            className={`w-full font-bold py-3 px-4 rounded transition duration-300 ease-in-out ${
              disabled
                ? "bg-gray-400 cursor-not-allowed"
                : "bg-btn-blue hover:bg-blue-700 text-white"
            }`}
            onClick={handleReadDocument}
          >
            {translate("read_document")}
          </button>
        </div>
      </div>
    </>
  );
};

export default Footer;
