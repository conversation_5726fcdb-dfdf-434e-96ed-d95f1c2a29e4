// Utility functions for connected privacy policy

/**
 * Fetch region configuration from the config.json file
 * @param {string} baseUrl - The base URL for the application
 * @returns {Promise<Object>} The configuration data
 */
export const fetchRegionConfig = async (baseUrl = "") => {
  try {
    const response = await fetch(
      `${baseUrl}/connected-vehicle-privacy-policy/assets/config.json`
    );
    
    if (!response.ok) {
      throw new Error(`Failed to fetch config: ${response.status} ${response.statusText}`);
    }
    
    const data = await response.json();
    return data;
  } catch (err) {
    console.error("Error fetching region config:", err);
    throw err;
  }
};

/**
 * Determine region from country code
 * @param {string} country - The country code
 * @param {Object} regions - The regions mapping object
 * @returns {string|null} The region code or null if not found
 */
export const getRegionFromCountry = (country, regions) => {
  for (const [region, countries] of Object.entries(regions)) {
    if (Array.isArray(countries) && countries.includes(country)) {
      return region;
    }
  }
  return null;
};

/**
 * Build regions mapping from configuration data
 * @param {Object} configData - The configuration data
 * @returns {Object} The regions mapping
 */
export const buildRegionsMapping = (configData) => {
  const regions = {};
  for (const [regionCode, regionData] of Object.entries(configData.regions)) {
    regions[regionCode] = Object.entries(regionData)
      .filter(([key, value]) => key !== "name" && typeof value === "object")
      .map(([key]) => key);
  }
  return regions;
};

/**
 * Validate country and language parameters
 * @param {string} country - The country code
 * @param {string} language - The language code
 * @param {string} baseUrl - The base URL for the application
 * @returns {Promise<Object>} Validation result with isValid flag and additional data
 */
export const validateCountryAndLanguage = async (country, language, baseUrl = "") => {
  try {
    const data = await fetchRegionConfig(baseUrl);
    const EEA = data.EEA;
    const regions = buildRegionsMapping(data);

    // Handle EEA countries
    let processedCountry = country;
    if (EEA.includes(country)) {
      processedCountry = "EEA";
    }

    const region = getRegionFromCountry(processedCountry, regions);

    if (region === null) {
      return { 
        isValid: false, 
        error: "Region not found",
        details: `Country '${country}' is not supported`
      };
    }

    if (data.regions[region][processedCountry] === undefined) {
      return { 
        isValid: false, 
        error: "Country not found in region",
        details: `Country '${processedCountry}' not found in region '${region}'`
      };
    }

    const languages = data.regions[region][processedCountry].languages;

    if (!languages.includes(language)) {
      return { 
        isValid: false, 
        error: "Language not supported",
        details: `Language '${language}' not supported for country '${processedCountry}'. Available languages: ${languages.join(', ')}`
      };
    }

    return { 
      isValid: true, 
      region, 
      country: processedCountry,
      availableLanguages: languages
    };
  } catch (err) {
    console.error("Validation error:", err);
    return { 
      isValid: false, 
      error: "Validation failed",
      details: err.message
    };
  }
};

/**
 * Generate asset URL for the PDF
 * @param {string} region - The region code
 * @param {string} country - The country code
 * @param {string} language - The language code
 * @param {string} baseUrl - The base URL for the application
 * @returns {string} The asset URL
 */
export const getAssetUrl = (region, country, language, baseUrl = "") => {
  return `${baseUrl}/connected-vehicle-privacy-policy/pdf/${region}_${country}_${language}.pdf`;
};

/**
 * Fetch asset headers from the CDN
 * @param {string} url - The asset URL
 * @returns {Promise<Object>} Asset information including headers
 */
export const fetchAssetHeaders = async (url) => {
  try {
    const response = await fetch(url, { method: "HEAD" });
    const headers = {};
    
    // Extract relevant headers
    headers.contentType = response.headers.get("content-type");
    headers.contentLength = response.headers.get("content-length");
    headers.lastModified = response.headers.get("last-modified");
    headers.etag = response.headers.get("etag");
    headers.cacheControl = response.headers.get("cache-control");
    headers.expires = response.headers.get("expires");
    headers.server = response.headers.get("server");
    
    return {
      url,
      status: response.status,
      statusText: response.statusText,
      headers,
      redirectUrl: url,
      pdfUrl: url,
      timestamp: new Date().toISOString()
    };
  } catch (err) {
    console.error("Error fetching asset headers:", err);
    throw new Error(`Failed to fetch asset headers: ${err.message}`);
  }
};

/**
 * Process request based on query parameters
 * @param {string} country - The country code
 * @param {string} language - The language code
 * @param {Object} queryParams - Additional query parameters
 * @param {string} baseUrl - The base URL for the application
 * @returns {Promise<Object>} Processing result
 */
export const processPrivacyPolicyRequest = async (country, language, queryParams = {}, baseUrl = "") => {
  const { pdf, view, type } = queryParams;
  
  try {
    // Validate country and language
    const validation = await validateCountryAndLanguage(country, language, baseUrl);
    
    if (!validation.isValid) {
      return {
        success: false,
        error: validation.error,
        details: validation.details
      };
    }

    const { region, country: processedCountry } = validation;
    const assetUrl = getAssetUrl(region, processedCountry, language, baseUrl);

    // Determine response type
    // type=json is for pure API response, view=json is for UI display of JSON
    const responseType = view === "json" || type === "json" ? "json" :
                        (pdf === "true" || pdf === true) ? "redirect" : "display";

    // Get asset information
    const assetInfo = await fetchAssetHeaders(assetUrl);

    return {
      success: true,
      responseType,
      assetInfo,
      validation
    };
    
  } catch (err) {
    console.error("Error processing request:", err);
    return {
      success: false,
      error: "Processing failed",
      details: err.message
    };
  }
};
