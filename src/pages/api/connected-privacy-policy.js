// API route for connected privacy policy
// Handles server-side JSON responses and redirects

import { processPrivacyPolicyRequest } from "../../utils/connectedPrivacyPolicy";

export default async function handler(req, res) {
  const { country, language, pdf, view, type } = req.query;
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || "";

  try {
    // Validate required parameters
    if (!country || !language) {
      return res.status(400).json({
        error: "Missing required parameters",
        message: "Both 'country' and 'language' parameters are required"
      });
    }

    // Process the request using the utility function
    const result = await processPrivacyPolicyRequest(country, language, { pdf, view, type }, baseUrl);

    if (!result.success) {
      return res.status(400).json({
        error: result.error,
        message: result.details || result.error
      });
    }

    const { responseType, assetInfo } = result;

    // Handle different response types
    switch (responseType) {
      case "json":
        // Return JSON response with asset info
        return res.status(200).json(assetInfo);

      case "redirect":
        // Redirect to CDN
        return res.redirect(302, assetInfo.url);

      case "display":
      default:
        // Return asset info for PDF display
        return res.status(200).json({
          ...assetInfo,
          displayMode: "pdf"
        });
    }

  } catch (err) {
    console.error("API Error:", err);
    return res.status(500).json({
      error: "Internal server error",
      message: "Failed to process request"
    });
  }
}
