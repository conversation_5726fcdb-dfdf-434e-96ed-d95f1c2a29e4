"use client";
import { useEffect } from "react";
import { useRouter } from "next/navigation";

function Index() {
  const router = useRouter();

  const handleNavigation = (path) => {
    router.push(path);
  };

  return (
    <div className="flex flex-col md:flex-row min-h-screen">
      <div className="hidden md:block md:w-2/3 relative">
        <img
          src="/background.png"
          alt="Background"
          className="object-cover w-full h-full absolute inset-0"
        />
      </div>
      <div className="w-full md:w-1/3 flex-grow flex flex-col min-h-screen bg-white">
        <div className="p-8 flex flex-col h-full">
          {/* Logo and Title */}
          <div className="flex items-center mb-10">
            <img
              src="/logo.png"
              alt="Stellantis Logo"
              className="w-16 h-16 mr-4"
            />
            <h1 className="text-2xl font-bold text-black">Stellantis Privacy Hub</h1>
          </div>
          
          {/* Buttons Section */}
          <div className="flex flex-col items-center justify-start flex-grow space-y-6">
            <button 
              onClick={() => handleNavigation('/rtn')}
              className="w-full py-3 px-4 bg-gray-200 hover:bg-gray-300 text-gray-800 rounded transition duration-300 ease-in-out"
            >
              RTN Privacy Policy
            </button>
            
            <button 
              onClick={() => handleNavigation('/connected-privacy-policy')}
              className="w-full py-3 px-4 bg-gray-200 hover:bg-gray-300 text-gray-800 rounded transition duration-300 ease-in-out"
            >
              Connected Vehicle Privacy Policy
            </button>
            
            <button 
              onClick={() => handleNavigation('/general-privacy-policy')}
              className="w-full py-3 px-4 bg-gray-200 hover:bg-gray-300 text-gray-800 rounded transition duration-300 ease-in-out"
            >
              General Vehicle Privacy Policy
            </button>

            <button 
              onClick={() => handleNavigation('/terms-and-condition-app')}
              className="w-full py-3 px-4 bg-gray-200 hover:bg-gray-300 text-gray-800 rounded transition duration-300 ease-in-out"
            >
              Terms and Conditions
            </button>
          </div>
          
          {/* Footer that stays at bottom */}
          <div className="mt-auto pt-8 text-center text-sm text-gray-600">
            <p>© 2025 Stellantis. All rights reserved.</p>
          </div>
        </div>
      </div>
    </div>
  ); 
}

export default Index;