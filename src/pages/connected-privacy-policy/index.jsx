"use client";
import React from "react";
import Form from "../../components/connected-privacy-policy/Form";
import { useEffect, useState } from "react";
import { useRouter } from "next/router";
import { ThreeDots } from "react-loader-spinner";
import PdfViewer from "../../components/PdfViewer";
import { processPrivacyPolicyRequest } from "../../utils/connectedPrivacyPolicy";

function Index() {
  const router = useRouter();
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || "";
  const [pdfUrl, setPdfUrl] = useState(null);
  const [pageNo, setPageNo] = useState(0);
  const [homepage, setHomepage] = useState(true);
  const [hideLoader, setHideLoader] = useState(false);
  const [assetInfo, setAssetInfo] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Main processing function
  const processRequest = async (country, language, queryParams) => {
    setLoading(true);
    setError(null);

    try {
      const result = await processPrivacyPolicyRequest(country, language, queryParams, baseUrl);

      if (!result.success) {
        console.error("Processing failed:", result.error);
        setError(result.details || result.error);
        router.push("/connected-privacy-policy");
        return;
      }

      const { responseType, assetInfo: assetData } = result;

      // Handle different response types
      switch (responseType) {
        case "json":
          // For view=json, display JSON in UI (type=json is handled in useEffect with redirect)
          setAssetInfo(assetData);
          break;

        case "redirect":
          // Redirect to CDN
          window.location.href = assetData.url;
          break;

        case "display":
        default:
          // Display PDF
          setPdfUrl(assetData.url);
          break;
      }

    } catch (err) {
      console.error("Error processing request:", err);
      setError(err.message || "An unexpected error occurred");
      setHomepage(true);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (!router.isReady) return;

    const {
      country,
      language,
      hideLoader: hideLoaderParam,
      pdf,
      page,
      view,
      type,
    } = router.query;

    // Handle pure JSON API response
    if (type === "json") {
      handleJsonApiResponse(country, language, { pdf, view, type });
      return;
    }

    // Set loader visibility
    if (hideLoaderParam === "true") {
      setHideLoader(true);
    }

    // Set page number
    if (page) {
      setPageNo(parseInt(page, 10) || 0);
    }

    // Check if we should show homepage
    if (!country || !language) {
      setHomepage(true);
      return;
    }

    setHomepage(false);

    // Process the request
    processRequest(country, language, { pdf, view, type });
  }, [router.isReady, router.query]);

  // Handle JSON API response - return pure JSON without HTML
  const handleJsonApiResponse = async (country, language, queryParams) => {
    try {
      // Validate required parameters
      if (!country || !language) {
        const errorResponse = {
          error: "Missing required parameters",
          message: "Both 'country' and 'language' parameters are required"
        };

        // Clear the page content and return JSON response
        document.body.innerHTML = '';
        document.body.style.margin = '0';
        document.body.style.padding = '0';
        document.body.style.fontFamily = 'monospace';
        document.body.style.whiteSpace = 'pre-wrap';
        document.body.textContent = JSON.stringify(errorResponse, null, 2);

        // Set response headers if possible
        if (typeof window !== 'undefined' && window.history) {
          document.title = 'JSON Response';
        }
        return;
      }

      // Process the request using the utility function
      const result = await processPrivacyPolicyRequest(country, language, queryParams, baseUrl);

      if (!result.success) {
        const errorResponse = {
          error: result.error,
          message: result.details || result.error
        };

        // Clear the page content and return JSON response
        document.body.innerHTML = '';
        document.body.style.margin = '0';
        document.body.style.padding = '0';
        document.body.style.fontFamily = 'monospace';
        document.body.style.whiteSpace = 'pre-wrap';
        document.body.textContent = JSON.stringify(errorResponse, null, 2);
        return;
      }

      const { assetInfo } = result;

      // Clear the page content and return pure JSON response
      document.body.innerHTML = '';
      document.body.style.margin = '0';
      document.body.style.padding = '0';
      document.body.style.fontFamily = 'monospace';
      document.body.style.whiteSpace = 'pre-wrap';
      document.body.style.backgroundColor = '#f8f9fa';
      document.body.textContent = JSON.stringify(assetInfo, null, 2);

      // Set document title
      document.title = 'JSON Response';

    } catch (err) {
      console.error("Error handling JSON API response:", err);

      const errorResponse = {
        error: "Internal server error",
        message: "Failed to process request"
      };

      // Clear the page content and return JSON response
      document.body.innerHTML = '';
      document.body.style.margin = '0';
      document.body.style.padding = '0';
      document.body.style.fontFamily = 'monospace';
      document.body.style.whiteSpace = 'pre-wrap';
      document.body.textContent = JSON.stringify(errorResponse, null, 2);
    }
  };

  // Render error state
  if (error) {
    return (
      <div className="w-full h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-2xl w-full mx-4">
          <div className="bg-white rounded-lg shadow-lg p-6 border-l-4 border-red-500">
            <h1 className="text-2xl font-bold mb-4 text-red-800">Error</h1>
            <p className="text-red-600 mb-4">{error}</p>
            <div className="flex gap-3">
              <button
                onClick={() => window.history.back()}
                className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700 transition-colors"
              >
                Go Back
              </button>
              <button
                onClick={() => window.location.reload()}
                className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors"
              >
                Retry
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Render JSON response
  if (assetInfo) {
    return (
      <div className="w-full h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-4xl w-full mx-4">
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h1 className="text-2xl font-bold mb-4 text-gray-800">Asset Information</h1>
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h3 className="font-semibold text-gray-700">URL:</h3>
                  <p className="text-sm text-blue-600 break-all">{assetInfo.url}</p>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-700">Status:</h3>
                  <p className="text-sm text-gray-600">{assetInfo.status} - {assetInfo.statusText}</p>
                </div>
                {assetInfo.timestamp && (
                  <div>
                    <h3 className="font-semibold text-gray-700">Timestamp:</h3>
                    <p className="text-sm text-gray-600">{new Date(assetInfo.timestamp).toLocaleString()}</p>
                  </div>
                )}
              </div>

              <div>
                <h3 className="font-semibold text-gray-700 mb-2">Headers:</h3>
                <div className="bg-gray-50 rounded p-4 max-h-64 overflow-y-auto">
                  <pre className="text-sm text-gray-600 whitespace-pre-wrap">
                    {JSON.stringify(assetInfo.headers, null, 2)}
                  </pre>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h3 className="font-semibold text-gray-700">Redirect URL:</h3>
                  <p className="text-sm text-blue-600 break-all">{assetInfo.redirectUrl}</p>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-700">PDF URL:</h3>
                  <p className="text-sm text-blue-600 break-all">{assetInfo.pdfUrl}</p>
                </div>
              </div>
            </div>

            <div className="mt-6 flex flex-wrap gap-3">
              <a
                href={assetInfo.url}
                target="_blank"
                rel="noopener noreferrer"
                className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors"
              >
                Open PDF
              </a>
              <button
                onClick={() => navigator.clipboard.writeText(JSON.stringify(assetInfo, null, 2))}
                className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700 transition-colors"
              >
                Copy JSON
              </button>
              <button
                onClick={() => window.history.back()}
                className="bg-gray-300 text-gray-700 px-4 py-2 rounded hover:bg-gray-400 transition-colors"
              >
                Go Back
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Render homepage form
  if (homepage) {
    return (
      <div className="relative min-h-screen flex">
        <div className="relative w-2/3 hidden md:block">
          <img
            src={`${baseUrl}/assets/background.png`}
            alt="Background"
            className="object-cover w-full h-full"
          />
        </div>
        <div className="w-full md:w-1/3 flex items-center justify-center z-10 bg-white">
          <Form hideLoader={hideLoader} />
        </div>
      </div>
    );
  }

  // Render PDF viewer or loading state
  return (
    <div className="w-full h-screen">
      {pdfUrl ? (
        <PdfViewer pdfUrl={pdfUrl} hideLoader={hideLoader} pageno={pageNo} />
      ) : (
        <>
          {!hideLoader && loading && (
            <div className="w-full h-screen flex items-center justify-center text-3xl bg-white text-black">
              <ThreeDots
                visible={true}
                height="80"
                width="80"
                color="#4766FF"
                radius="9"
                ariaLabel="three-dots-loading"
                wrapperStyle={{}}
                wrapperClass=""
              />
            </div>
          )}
        </>
      )}
    </div>
  );
}

// Server-side props to handle JSON responses
export async function getServerSideProps(context) {
  // Import the utility function for server-side use
  const { processPrivacyPolicyRequest } = await import("../../utils/connectedPrivacyPolicy");

  const { query, res } = context;
  const { country, language, type } = query;

  // Handle pure JSON API response
  if (type === "json") {
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || "";

    try {
      // Validate required parameters
      if (!country || !language) {
        const errorResponse = {
          error: "Missing required parameters",
          message: "Both 'country' and 'language' parameters are required"
        };

        res.setHeader('Content-Type', 'application/json');
        res.statusCode = 400;
        res.end(JSON.stringify(errorResponse));
        return { props: {} };
      }

      // Process the request using the utility function
      const result = await processPrivacyPolicyRequest(country, language, query, baseUrl);

      if (!result.success) {
        const errorResponse = {
          error: result.error,
          message: result.details || result.error
        };

        res.setHeader('Content-Type', 'application/json');
        res.statusCode = 400;
        res.end(JSON.stringify(errorResponse));
        return { props: {} };
      }

      const { assetInfo } = result;

      // Return pure JSON response
      res.setHeader('Content-Type', 'application/json');
      res.statusCode = 200;
      res.end(JSON.stringify(assetInfo));
      return { props: {} };

    } catch (err) {
      console.error("Server-side error:", err);

      const errorResponse = {
        error: "Internal server error",
        message: "Failed to process request"
      };

      res.setHeader('Content-Type', 'application/json');
      res.statusCode = 500;
      res.end(JSON.stringify(errorResponse));
      return { props: {} };
    }
  }

  // For non-JSON requests, return empty props (client-side rendering)
  return {
    props: {}
  };
}

export default Index;
